# aiMacs项目 .gitignore

# Python环境
venv/
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 日志文件
*.log
logs/
debug.txt
debuginfo.txt
masterDebug*.txt
masterDebug*.csv

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*_backup/
*.save
*.bak

# 临时文件
*.tmp
*.temp
*~

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
*.lnk

# 项目特定 - 大文件
*.sql
!*template*.sql
*.mbw

# 编译文件
*.exe
*.dll
*.bin

# 本地配置
config_local.py
local_*.json
