# aiMacs 项目重构方案

## 当前问题
- 大量重复的代码文件 (aiMacs.py, PCSclasses.py等)
- 相同的配置文件在多个目录中重复
- 维护困难，修改需要在多处同步
- Git仓库体积庞大

## 推荐的新项目结构

```
aiMacs/
├── core/                           # 核心库
│   ├── aiMacs/                     # 主要控制代码
│   │   ├── __init__.py
│   │   ├── aiMacs.py
│   │   ├── PCSclasses.py
│   │   ├── utilities.py
│   │   ├── control.py
│   │   ├── meter.py
│   │   └── modbus.py
│   └── cloud/                      # Web应用核心
│       ├── epc/
│       ├── gateway/
│       └── repl/
├── configs/                        # 配置模板
│   ├── templates/
│   │   ├── default.mbp
│   │   ├── config_template.py
│   │   └── database_template.sql
│   └── projects/                   # 项目特定配置
│       ├── HVI1/
│       ├── NKBMW/
│       ├── IPRT/
│       └── VistaPaint/
├── deployments/                    # 部署脚本
│   ├── deploy.py
│   ├── docker/
│   └── scripts/
└── projects/                       # 项目实例 (仅配置差异)
    ├── F01_Core/
    ├── F06_HVI1/
    ├── F08_NKBMW/
    └── F12_VistaPaint/
```

## 实施步骤

### 第一阶段：创建核心库
1. 提取共同代码到 `core/` 目录
2. 创建配置系统来处理项目差异
3. 建立模板系统

### 第二阶段：Git子模块化
1. 将 `core/` 作为独立Git仓库
2. 各项目作为子模块引用核心库
3. 使用配置文件管理项目差异

### 第三阶段：自动化部署
1. 创建部署脚本
2. 基于配置自动生成项目实例
3. CI/CD集成

## 配置管理方案

### 项目配置文件示例 (projects/F06_HVI1/config.yaml)
```yaml
project:
  name: "HVI1"
  type: "energy_storage"
  
database:
  name: "local_ess_HVI1"
  host: "localhost"
  
pcs:
  model: "SIN500K"
  count: 9
  power: 500
  
bms:
  model: "Gotion"
  strings: 25
  
network:
  local_ip: "*************"
  cloud_endpoint: "https://cloud.epcenergy.com"
```

## 迁移策略

### 选项1：渐进式迁移
- 保持现有结构
- 逐步引入符号链接
- 测试无问题后完全重构

### 选项2：新分支重构
- 创建新分支进行重构
- 并行维护旧版本
- 验证后合并

### 选项3：新仓库
- 创建全新仓库结构
- 迁移历史记录
- 逐步废弃旧仓库

## 工具和脚本

### 重复文件检测脚本
```bash
#!/bin/bash
# 检测重复文件
find E02_Build -type f -exec md5sum {} \; | sort | uniq -d -w 32
```

### 自动部署脚本
```python
# deploy.py - 基于配置生成项目实例
def deploy_project(config_file, target_dir):
    # 读取配置
    # 复制核心文件
    # 应用项目特定配置
    # 生成数据库脚本
```

## 预期收益

1. **减少90%的重复代码**
2. **简化维护** - 核心修改一次生效
3. **提高一致性** - 统一的代码基础
4. **加速新项目部署**
5. **减少Git仓库大小**
6. **改善代码质量**

## 风险和注意事项

1. **迁移复杂性** - 需要仔细测试
2. **现有部署影响** - 需要迁移计划
3. **团队培训** - 新的工作流程
4. **配置复杂性** - 需要良好的文档

## 下一步行动

1. 备份当前仓库
2. 分析项目间的具体差异
3. 设计配置系统
4. 创建概念验证
5. 制定详细迁移计划
