#!/usr/bin/env python3
"""
为aiMacs项目创建符号链接，减少重复文件
"""
import os
import sys
import shutil
from pathlib import Path

def backup_directory(src_dir):
    """备份目录"""
    backup_dir = f"{src_dir}_backup"
    if os.path.exists(backup_dir):
        shutil.rmtree(backup_dir)
    shutil.copytree(src_dir, backup_dir)
    print(f"已备份 {src_dir} 到 {backup_dir}")

def create_symlink_structure(root_dir):
    """创建符号链接结构"""
    
    # 定义基础组件路径
    base_minipc = os.path.join(root_dir, "F00_miniPC")
    base_cloud = os.path.join(root_dir, "F00_Cloud")
    
    if not os.path.exists(base_minipc):
        print(f"错误: 基础目录不存在 {base_minipc}")
        return False
    
    if not os.path.exists(base_cloud):
        print(f"错误: 基础目录不存在 {base_cloud}")
        return False
    
    # 查找所有项目目录
    project_dirs = []
    for item in os.listdir(root_dir):
        item_path = os.path.join(root_dir, item)
        if os.path.isdir(item_path) and item.startswith('F') and item not in ['F00_miniPC', 'F00_Cloud']:
            project_dirs.append(item_path)
    
    print(f"找到 {len(project_dirs)} 个项目目录")
    
    for project_dir in project_dirs:
        project_name = os.path.basename(project_dir)
        print(f"\n处理项目: {project_name}")
        
        # 备份项目目录
        backup_directory(project_dir)
        
        # 处理aiMacs目录
        aimacs_dir = os.path.join(project_dir, "aiMacs")
        if os.path.exists(aimacs_dir):
            # 检查是否有项目特定的文件
            base_aimacs = os.path.join(base_minipc, "aiMacs")
            project_specific_files = find_project_specific_files(aimacs_dir, base_aimacs)
            
            if project_specific_files:
                print(f"  发现项目特定文件: {project_specific_files}")
                # 保留项目特定文件，其他创建符号链接
                create_selective_symlinks(aimacs_dir, base_aimacs, project_specific_files)
            else:
                # 完全替换为符号链接
                print(f"  替换 aiMacs 目录为符号链接")
                shutil.rmtree(aimacs_dir)
                os.symlink(os.path.relpath(base_aimacs, project_dir), aimacs_dir)
        
        # 处理html目录
        html_dir = os.path.join(project_dir, "html")
        if os.path.exists(html_dir):
            base_html = os.path.join(base_cloud)
            project_specific_files = find_project_specific_files(html_dir, base_html)
            
            if project_specific_files:
                print(f"  发现项目特定HTML文件: {project_specific_files}")
                create_selective_symlinks(html_dir, base_html, project_specific_files)
            else:
                print(f"  替换 html 目录为符号链接")
                shutil.rmtree(html_dir)
                os.symlink(os.path.relpath(base_html, project_dir), html_dir)
        
        # 处理重复的配置文件
        handle_duplicate_configs(project_dir, base_minipc)

def find_project_specific_files(project_dir, base_dir):
    """查找项目特定的文件（与基础版本不同的文件）"""
    project_specific = []
    
    if not os.path.exists(base_dir):
        return []
    
    for root, dirs, files in os.walk(project_dir):
        for file in files:
            project_file = os.path.join(root, file)
            rel_path = os.path.relpath(project_file, project_dir)
            base_file = os.path.join(base_dir, rel_path)
            
            if not os.path.exists(base_file):
                project_specific.append(rel_path)
            else:
                # 比较文件内容
                try:
                    with open(project_file, 'rb') as f1, open(base_file, 'rb') as f2:
                        if f1.read() != f2.read():
                            project_specific.append(rel_path)
                except:
                    project_specific.append(rel_path)
    
    return project_specific

def create_selective_symlinks(project_dir, base_dir, keep_files):
    """创建选择性符号链接，保留项目特定文件"""
    
    # 创建临时目录保存项目特定文件
    temp_dir = f"{project_dir}_temp"
    os.makedirs(temp_dir, exist_ok=True)
    
    # 保存项目特定文件
    for file_path in keep_files:
        src = os.path.join(project_dir, file_path)
        dst = os.path.join(temp_dir, file_path)
        os.makedirs(os.path.dirname(dst), exist_ok=True)
        shutil.copy2(src, dst)
    
    # 删除原目录
    shutil.rmtree(project_dir)
    
    # 创建符号链接到基础目录
    os.symlink(os.path.relpath(base_dir, os.path.dirname(project_dir)), project_dir)
    
    # 恢复项目特定文件（覆盖符号链接的内容）
    for file_path in keep_files:
        src = os.path.join(temp_dir, file_path)
        dst = os.path.join(project_dir, file_path)
        
        # 如果目标是符号链接的一部分，需要特殊处理
        dst_dir = os.path.dirname(dst)
        if not os.path.exists(dst_dir):
            os.makedirs(dst_dir, exist_ok=True)
        
        shutil.copy2(src, dst)
    
    # 清理临时目录
    shutil.rmtree(temp_dir)

def handle_duplicate_configs(project_dir, base_dir):
    """处理重复的配置文件"""
    config_extensions = ['.mbp', '.csv', '.conf', '.txt']
    
    for file in os.listdir(project_dir):
        if any(file.endswith(ext) for ext in config_extensions):
            project_file = os.path.join(project_dir, file)
            base_file = os.path.join(base_dir, file)
            
            if os.path.exists(base_file):
                # 比较文件
                try:
                    with open(project_file, 'rb') as f1, open(base_file, 'rb') as f2:
                        if f1.read() == f2.read():
                            print(f"  删除重复配置文件: {file}")
                            os.remove(project_file)
                            os.symlink(os.path.relpath(base_file, project_dir), project_file)
                except:
                    pass

def verify_symlinks(root_dir):
    """验证符号链接是否正确创建"""
    print("\n=== 验证符号链接 ===")
    
    for item in os.listdir(root_dir):
        item_path = os.path.join(root_dir, item)
        if os.path.isdir(item_path) and item.startswith('F') and item not in ['F00_miniPC', 'F00_Cloud']:
            
            aimacs_link = os.path.join(item_path, "aiMacs")
            html_link = os.path.join(item_path, "html")
            
            if os.path.islink(aimacs_link):
                target = os.readlink(aimacs_link)
                print(f"  {item}/aiMacs -> {target}")
            
            if os.path.islink(html_link):
                target = os.readlink(html_link)
                print(f"  {item}/html -> {target}")

def main():
    if len(sys.argv) > 1:
        root_dir = sys.argv[1]
    else:
        root_dir = "E02_Build"
    
    if not os.path.exists(root_dir):
        print(f"目录不存在: {root_dir}")
        sys.exit(1)
    
    print(f"开始为 {root_dir} 创建符号链接结构...")
    print("警告: 此操作会修改项目结构，请确保已备份重要数据!")
    
    response = input("是否继续? (y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        sys.exit(0)
    
    create_symlink_structure(root_dir)
    verify_symlinks(root_dir)
    
    print("\n=== 完成 ===")
    print("符号链接创建完成。如果有问题，可以从 *_backup 目录恢复。")

if __name__ == "__main__":
    main()
