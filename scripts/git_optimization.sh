#!/bin/bash
# aiMacs Git仓库优化脚本

set -e

echo "=== aiMacs Git仓库优化工具 ==="

# 检查是否在Git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "错误: 当前目录不是Git仓库"
    exit 1
fi

# 显示当前仓库状态
echo "当前仓库状态:"
echo "分支: $(git branch --show-current)"
echo "仓库大小: $(du -sh .git | cut -f1)"
echo "文件数量: $(find . -type f | wc -l)"

# 功能菜单
show_menu() {
    echo ""
    echo "请选择操作:"
    echo "1. 分析重复文件"
    echo "2. 创建符号链接结构"
    echo "3. 清理Git历史中的大文件"
    echo "4. 创建新的优化分支"
    echo "5. 生成项目重构报告"
    echo "6. 设置Git LFS (大文件支持)"
    echo "7. 退出"
    echo ""
}

# 分析重复文件
analyze_duplicates() {
    echo "正在分析重复文件..."
    if [ -f "scripts/find_duplicates.py" ]; then
        python3 scripts/find_duplicates.py E02_Build
    else
        echo "错误: 找不到 scripts/find_duplicates.py"
    fi
}

# 创建符号链接
create_symlinks() {
    echo "警告: 此操作会修改项目结构!"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ -f "scripts/create_symlinks.py" ]; then
            python3 scripts/create_symlinks.py E02_Build
        else
            echo "错误: 找不到 scripts/create_symlinks.py"
        fi
    fi
}

# 清理Git历史中的大文件
clean_git_history() {
    echo "正在查找Git历史中的大文件..."
    
    # 查找大文件
    git rev-list --objects --all | \
    git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | \
    sed -n 's/^blob //p' | \
    sort --numeric-sort --key=2 | \
    tail -20
    
    echo ""
    echo "以上是Git历史中最大的20个文件"
    echo "如需清理，请手动运行:"
    echo "git filter-branch --force --index-filter 'git rm --cached --ignore-unmatch <文件路径>' --prune-empty --tag-name-filter cat -- --all"
    echo ""
    echo "警告: 此操作会重写Git历史，请谨慎使用!"
}

# 创建优化分支
create_optimization_branch() {
    echo "创建优化分支..."
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        echo "错误: 有未提交的更改，请先提交或暂存"
        return 1
    fi
    
    # 创建新分支
    branch_name="optimization-$(date +%Y%m%d)"
    git checkout -b "$branch_name"
    
    echo "已创建并切换到分支: $branch_name"
    echo "现在可以安全地进行结构优化"
}

# 生成项目报告
generate_report() {
    echo "生成项目分析报告..."
    
    report_file="project_analysis_$(date +%Y%m%d).md"
    
    cat > "$report_file" << EOF
# aiMacs 项目分析报告
生成时间: $(date)

## Git仓库信息
- 分支: $(git branch --show-current)
- 最新提交: $(git log -1 --oneline)
- 仓库大小: $(du -sh .git | cut -f1)
- 总文件数: $(find . -type f | wc -l)

## 目录结构
\`\`\`
$(tree E02_Build -L 2 2>/dev/null || find E02_Build -type d | head -20)
\`\`\`

## 大文件分析
\`\`\`
$(find E02_Build -type f -size +1M -exec ls -lh {} \; | head -10)
\`\`\`

## 重复文件统计
EOF
    
    if [ -f "scripts/find_duplicates.py" ]; then
        python3 scripts/find_duplicates.py E02_Build >> "$report_file"
    fi
    
    echo "报告已生成: $report_file"
}

# 设置Git LFS
setup_git_lfs() {
    echo "设置Git LFS..."
    
    # 检查Git LFS是否安装
    if ! command -v git-lfs &> /dev/null; then
        echo "Git LFS未安装，请先安装:"
        echo "  macOS: brew install git-lfs"
        echo "  Ubuntu: sudo apt install git-lfs"
        return 1
    fi
    
    # 初始化Git LFS
    git lfs install
    
    # 添加大文件类型到LFS
    git lfs track "*.sql"
    git lfs track "*.mbw"
    git lfs track "*.bin"
    git lfs track "*.exe"
    git lfs track "*.dll"
    
    # 提交.gitattributes
    git add .gitattributes
    git commit -m "Add Git LFS tracking for large files"
    
    echo "Git LFS设置完成"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选择 (1-7): " choice
    
    case $choice in
        1)
            analyze_duplicates
            ;;
        2)
            create_symlinks
            ;;
        3)
            clean_git_history
            ;;
        4)
            create_optimization_branch
            ;;
        5)
            generate_report
            ;;
        6)
            setup_git_lfs
            ;;
        7)
            echo "退出"
            exit 0
            ;;
        *)
            echo "无效选择，请重新输入"
            ;;
    esac
    
    echo ""
    read -p "按Enter继续..."
done
