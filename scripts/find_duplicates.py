#!/usr/bin/env python3
"""
检测aiMacs项目中的重复文件
"""
import os
import hashlib
import sys
from collections import defaultdict
from pathlib import Path

def calculate_md5(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return None

def find_duplicates(root_dir):
    """查找重复文件"""
    file_hashes = defaultdict(list)
    
    # 遍历所有文件
    for root, dirs, files in os.walk(root_dir):
        # 跳过隐藏目录和常见的临时目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'logs']]
        
        for file in files:
            # 跳过隐藏文件和临时文件
            if file.startswith('.') or file.endswith(('.pyc', '.log', '.tmp')):
                continue
                
            file_path = os.path.join(root, file)
            file_hash = calculate_md5(file_path)
            
            if file_hash:
                file_hashes[file_hash].append(file_path)
    
    return file_hashes

def analyze_duplicates(file_hashes, root_dir):
    """分析重复文件"""
    duplicates = {h: paths for h, paths in file_hashes.items() if len(paths) > 1}
    
    print(f"=== aiMacs 重复文件分析报告 ===\n")
    print(f"扫描目录: {root_dir}")
    print(f"总文件数: {sum(len(paths) for paths in file_hashes.values())}")
    print(f"唯一文件数: {len(file_hashes)}")
    print(f"重复文件组数: {len(duplicates)}")
    print(f"重复文件总数: {sum(len(paths) for paths in duplicates.values())}")
    
    # 按文件类型分类
    by_extension = defaultdict(list)
    total_wasted_space = 0
    
    for file_hash, paths in duplicates.items():
        if len(paths) > 1:
            # 计算浪费的空间
            file_size = os.path.getsize(paths[0])
            wasted_space = file_size * (len(paths) - 1)
            total_wasted_space += wasted_space
            
            # 按扩展名分类
            ext = Path(paths[0]).suffix.lower()
            by_extension[ext].append((file_hash, paths, file_size, wasted_space))
    
    print(f"浪费的存储空间: {total_wasted_space / 1024 / 1024:.2f} MB\n")
    
    # 按文件类型显示重复情况
    for ext, items in sorted(by_extension.items()):
        print(f"=== {ext or '无扩展名'} 文件重复情况 ===")
        ext_wasted = sum(item[3] for item in items)
        print(f"重复组数: {len(items)}, 浪费空间: {ext_wasted / 1024:.2f} KB\n")
        
        for file_hash, paths, file_size, wasted_space in sorted(items, key=lambda x: x[3], reverse=True)[:5]:
            print(f"文件大小: {file_size / 1024:.2f} KB, 重复{len(paths)}次, 浪费: {wasted_space / 1024:.2f} KB")
            for path in paths:
                rel_path = os.path.relpath(path, root_dir)
                print(f"  - {rel_path}")
            print()
    
    return duplicates

def suggest_optimizations(duplicates, root_dir):
    """建议优化方案"""
    print("\n=== 优化建议 ===")
    
    # 分析aiMacs.py文件
    aimacs_files = []
    for file_hash, paths in duplicates.items():
        if any('aiMacs.py' in path for path in paths):
            aimacs_files.extend(paths)
    
    if aimacs_files:
        print(f"\n1. aiMacs.py 核心文件重复:")
        for path in aimacs_files:
            print(f"   - {os.path.relpath(path, root_dir)}")
        print("   建议: 提取到共享核心库")
    
    # 分析配置文件
    config_files = []
    for file_hash, paths in duplicates.items():
        if any(path.endswith(('.mbp', '.csv', '.conf')) for path in paths):
            config_files.extend(paths)
    
    if config_files:
        print(f"\n2. 配置文件重复 ({len(config_files)} 个):")
        print("   建议: 使用模板系统和项目特定配置")
    
    # 分析Web文件
    web_files = []
    for file_hash, paths in duplicates.items():
        if any(path.endswith(('.php', '.html', '.css', '.js')) for path in paths):
            web_files.extend(paths)
    
    if web_files:
        print(f"\n3. Web应用文件重复 ({len(web_files)} 个):")
        print("   建议: 使用Git子模块或符号链接")

def main():
    if len(sys.argv) > 1:
        root_dir = sys.argv[1]
    else:
        root_dir = "E02_Build"
    
    if not os.path.exists(root_dir):
        print(f"目录不存在: {root_dir}")
        sys.exit(1)
    
    print("正在扫描重复文件...")
    file_hashes = find_duplicates(root_dir)
    
    duplicates = analyze_duplicates(file_hashes, root_dir)
    suggest_optimizations(duplicates, root_dir)
    
    # 生成清理脚本
    print(f"\n=== 生成清理建议 ===")
    print("运行以下命令查看具体的重复文件:")
    print(f"python3 scripts/generate_cleanup.py {root_dir}")

if __name__ == "__main__":
    main()
