name: aiMacs CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME_CORE: ${{ github.repository }}/aimacs-core
  IMAGE_NAME_CLOUD: ${{ github.repository }}/aimacs-cloud

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black flake8 bandit safety
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    - name: Code formatting check
      run: black --check --diff .

    - name: Linting
      run: flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics

    - name: Security scan
      run: |
        bandit -r E02_Build/F00_miniPC/aiMacs/ -f json -o bandit-report.json || true
        safety check --json --output safety-report.json || true

    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # 单元测试
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10']

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-mock
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    - name: Run tests
      run: |
        pytest tests/ --cov=E02_Build/F00_miniPC/aiMacs/ --cov-report=xml --cov-report=html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 构建Docker镜像
  build:
    runs-on: ubuntu-latest
    needs: test
    outputs:
      image-digest-core: ${{ steps.build-core.outputs.digest }}
      image-digest-cloud: ${{ steps.build-cloud.outputs.digest }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata for Core
      id: meta-core
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_CORE }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Core image
      id: build-core
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.minipc
        push: true
        tags: ${{ steps.meta-core.outputs.tags }}
        labels: ${{ steps.meta-core.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Extract metadata for Cloud
      id: meta-cloud
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_CLOUD }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Cloud image
      id: build-cloud
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.cloud
        push: true
        tags: ${{ steps.meta-cloud.outputs.tags }}
        labels: ${{ steps.meta-cloud.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    needs: build
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME_CORE }}:${{ github.sha }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # 这里可以添加实际的部署脚本
        # 例如: kubectl apply -f k8s/staging/
        # 或者: ansible-playbook -i inventory/staging deploy.yml

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    needs: [build, security-scan]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        # 生产环境部署脚本
        # 需要人工审批后才能执行

  # 通知
  notify:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.deploy-staging.result }}" == "success" ] || [ "${{ needs.deploy-production.result }}" == "success" ]; then
          echo "Deployment successful!"
          # 发送成功通知 (Slack, Teams, Email等)
        else
          echo "Deployment failed!"
          # 发送失败通知
        fi
