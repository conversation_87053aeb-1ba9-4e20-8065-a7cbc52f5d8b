# aiMacs miniPC 控制系统容器
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 创建非root用户
RUN useradd -m -u 1000 aimacs && \
    mkdir -p /app/logs /app/configs && \
    chown -R aimacs:aimacs /app

# 复制应用代码
COPY core/aiMacs/ ./aiMacs/
COPY configs/ ./configs/

# 设置权限
RUN chown -R aimacs:aimacs /app

# 切换到非root用户
USER aimacs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import socket; s=socket.socket(); s.connect(('localhost', 8080)); s.close()" || exit 1

# 暴露端口
EXPOSE 8080 502

# 设置环境变量
ENV PYTHONPATH=/app
ENV PROJECT_CONFIG=/app/configs/default.yaml

# 启动命令
CMD ["python", "aiMacs/aiMacs.py"]
