version: '3.8'

services:
  # aiMacs 核心控制系统
  aimacs-core:
    build:
      context: .
      dockerfile: Dockerfile.minipc
    container_name: aimacs-core
    environment:
      - PROJECT_NAME=${PROJECT_NAME:-default}
      - PROJECT_CONFIG=/app/configs/projects/${PROJECT_NAME:-default}/config.yaml
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME:-local_ess}
      - DB_USER=${DB_USER:-epc_app}
      - DB_PASSWORD=${DB_PASSWORD:-@20201231}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "502:502"    # Modbus TCP
      - "8080:8080"  # Health check
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - aimacs-network
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8080/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # aiMacs Cloud Web应用
  aimacs-cloud:
    build:
      context: .
      dockerfile: Dockerfile.cloud
    container_name: aimacs-cloud
    environment:
      - DB_HOST=mysql
      - DB_NAME=${DB_NAME:-local_ess}
      - DB_USER=${DB_USER:-epc_app}
      - DB_PASSWORD=${DB_PASSWORD:-@20201231}
      - CLOUD_URL=${CLOUD_URL:-http://localhost}
    volumes:
      - ./logs:/var/log/aimacs
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "80:80"
    depends_on:
      mysql:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - aimacs-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: aimacs-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-rootpassword}
      - MYSQL_DATABASE=${DB_NAME:-local_ess}
      - MYSQL_USER=${DB_USER:-epc_app}
      - MYSQL_PASSWORD=${DB_PASSWORD:-@20201231}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init:/docker-entrypoint-initdb.d:ro
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "3306:3306"
    restart: unless-stopped
    networks:
      - aimacs-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD:-rootpassword}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # TimescaleDB (时序数据库)
  timescaledb:
    image: timescale/timescaledb:latest-pg14
    container_name: aimacs-timescaledb
    environment:
      - POSTGRES_DB=${TIMESCALE_DB:-aimacs_timeseries}
      - POSTGRES_USER=${TIMESCALE_USER:-postgres}
      - POSTGRES_PASSWORD=${TIMESCALE_PASSWORD:-postgres}
    volumes:
      - timescale_data:/var/lib/postgresql/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - aimacs-network

  # Redis (缓存)
  redis:
    image: redis:7-alpine
    container_name: aimacs-redis
    volumes:
      - redis_data:/data
      - /etc/localtime:/etc/localtime:ro
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - aimacs-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: aimacs-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - aimacs-network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: aimacs-grafana
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    ports:
      - "3000:3000"
    restart: unless-stopped
    networks:
      - aimacs-network

volumes:
  mysql_data:
    driver: local
  timescale_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  aimacs-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
