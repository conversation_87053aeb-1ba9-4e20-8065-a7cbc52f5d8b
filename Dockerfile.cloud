# aiMacs Cloud Web应用容器
FROM php:8.1-apache

# 安装PHP扩展和系统依赖
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libpq-dev \
    unzip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd \
    && docker-php-ext-install pdo pdo_mysql mysqli zip \
    && rm -rf /var/lib/apt/lists/*

# 启用Apache模块
RUN a2enmod rewrite headers

# 设置Apache配置
COPY docker/apache/000-default.conf /etc/apache2/sites-available/000-default.conf

# 复制应用代码
COPY E02_Build/F00_Cloud/ /var/www/html/

# 设置权限
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# 创建日志目录
RUN mkdir -p /var/log/aimacs && \
    chown www-data:www-data /var/log/aimacs

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health.php || exit 1

# 暴露端口
EXPOSE 80

# 启动Apache
CMD ["apache2-foreground"]
